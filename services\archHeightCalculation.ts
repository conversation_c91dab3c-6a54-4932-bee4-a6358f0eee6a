/**
 * FootFit Arch Height Calculation - Competitive Advantage
 * 
 * This module provides the core competitive advantage for FootFit:
 * Converting CNN arch_ratio output to actual arch height in centimeters.
 * 
 * Based on anthropometric research: arch height = 6-12% of foot length
 * Provides unique "Length x Width x Arch Height" format for market differentiation
 */

import { log } from '@/utils/logger';

// =============================================================================
// INTERFACES & TYPES
// =============================================================================

export interface ArchHeightResult {
  archHeight: number;        // Arch height in centimeters
  archType: string;         // Low Arch, Normal Arch, High Arch
  formatted: string;        // "26.5 x 10.4 x 2.3cm" format
  percentage: number;       // Arch height as percentage of foot length
}

export interface FootMeasurementWithArch {
  length: number;           // Foot length in cm
  width: number;           // Foot width in cm
  archRatio: number;       // CNN output (0.25-0.75)
  heelRatio: number;       // CNN output (0.35-0.65)
  archHeight: number;      // Calculated arch height in cm
  archType: string;        // Arch classification
  formatted: string;       // Competitive advantage format
}

// =============================================================================
// ARCH HEIGHT CALCULATION (COMPETITIVE ADVANTAGE)
// =============================================================================

/**
 * Calculate arch height from CNN arch_ratio output
 * This is FootFit's key competitive advantage - only app measuring actual arch height in cm
 */
export function calculateArchHeight(footLength: number, archRatio: number): number {
  try {
    // Anthropometric conversion: arch_ratio (0.25-0.75) → 6-12% of foot length
    // Linear mapping from CNN output to anthropometric percentage
    const minRatio = 0.25;
    const maxRatio = 0.75;
    const minPercentage = 0.06; // 6% of foot length
    const maxPercentage = 0.12; // 12% of foot length
    
    // Ensure arch_ratio is within expected bounds
    const clampedRatio = Math.max(minRatio, Math.min(maxRatio, archRatio));
    
    // Linear interpolation to get arch percentage
    const normalizedRatio = (clampedRatio - minRatio) / (maxRatio - minRatio);
    const archPercentage = minPercentage + (normalizedRatio * (maxPercentage - minPercentage));
    
    // Calculate arch height in centimeters
    let archHeight = footLength * archPercentage;
    
    // Ensure realistic bounds (1.5-4.0cm for adult feet)
    archHeight = Math.max(1.5, Math.min(4.0, archHeight));
    
    // Round to 1 decimal place for precision
    return Math.round(archHeight * 10) / 10;
    
  } catch (error) {
    log.error('Error calculating arch height', 'ArchHeightCalculation', error);
    // Fallback to average arch height
    return Math.round((footLength * 0.09) * 10) / 10; // 9% average
  }
}

/**
 * Classify arch type based on arch height percentage
 */
export function classifyArchType(archHeight: number, footLength: number): string {
  try {
    const archPercentage = archHeight / footLength;
    
    if (archPercentage < 0.08) {
      return "Low Arch (Flat Feet)";
    } else if (archPercentage > 0.10) {
      return "High Arch";
    } else {
      return "Normal Arch";
    }
  } catch (error) {
    log.error('Error classifying arch type', 'ArchHeightCalculation', error);
    return "Normal Arch";
  }
}

/**
 * Format measurement in competitive "Length x Width x Arch Height" format
 * This is FootFit's unique market positioning
 */
export function formatArchMeasurement(length: number, width: number, archHeight: number): string {
  try {
    return `${length.toFixed(1)} x ${width.toFixed(1)} x ${archHeight.toFixed(1)}cm`;
  } catch (error) {
    log.error('Error formatting arch measurement', 'ArchHeightCalculation', error);
    return `${length || 0} x ${width || 0} x ${archHeight || 0}cm`;
  }
}

/**
 * Complete arch height analysis - main function for competitive advantage
 */
export function analyzeArchHeight(footLength: number, footWidth: number, archRatio: number): ArchHeightResult {
  try {
    log.info('Analyzing arch height for competitive advantage', 'ArchHeightCalculation', {
      footLength,
      footWidth,
      archRatio
    });
    
    // Calculate arch height (competitive advantage)
    const archHeight = calculateArchHeight(footLength, archRatio);
    
    // Classify arch type
    const archType = classifyArchType(archHeight, footLength);
    
    // Format in competitive display format
    const formatted = formatArchMeasurement(footLength, footWidth, archHeight);
    
    // Calculate percentage for additional info
    const percentage = (archHeight / footLength) * 100;
    
    const result: ArchHeightResult = {
      archHeight,
      archType,
      formatted,
      percentage: Math.round(percentage * 10) / 10
    };
    
    log.info('Arch height analysis completed', 'ArchHeightCalculation', result);
    
    return result;
    
  } catch (error) {
    log.error('Error in arch height analysis', 'ArchHeightCalculation', error);
    
    // Fallback result
    return {
      archHeight: Math.round((footLength * 0.09) * 10) / 10,
      archType: "Normal Arch",
      formatted: formatArchMeasurement(footLength, footWidth, footLength * 0.09),
      percentage: 9.0
    };
  }
}

/**
 * Process CNN output to include arch height measurements
 * Converts standard CNN output to FootFit's competitive advantage format
 */
export function processCNNOutputWithArchHeight(
  cnnOutput: number[]
): FootMeasurementWithArch {
  try {
    // CNN output format: [length_cm, width_cm, arch_ratio, heel_ratio]
    const [length, width, archRatio, heelRatio] = cnnOutput;
    
    log.info('Processing CNN output with arch height', 'ArchHeightCalculation', {
      length,
      width,
      archRatio,
      heelRatio
    });
    
    // Analyze arch height (competitive advantage)
    const archAnalysis = analyzeArchHeight(length, width, archRatio);
    
    const result: FootMeasurementWithArch = {
      length: Math.round(length * 10) / 10,
      width: Math.round(width * 10) / 10,
      archRatio: Math.round(archRatio * 100) / 100,
      heelRatio: Math.round(heelRatio * 100) / 100,
      archHeight: archAnalysis.archHeight,
      archType: archAnalysis.archType,
      formatted: archAnalysis.formatted
    };
    
    log.info('CNN output processed with arch height competitive advantage', 'ArchHeightCalculation', result);
    
    return result;
    
  } catch (error) {
    log.error('Error processing CNN output with arch height', 'ArchHeightCalculation', error);
    
    // Fallback result
    const fallbackLength = cnnOutput[0] || 26.0;
    const fallbackWidth = cnnOutput[1] || 10.0;
    const fallbackArchHeight = fallbackLength * 0.09;
    
    return {
      length: fallbackLength,
      width: fallbackWidth,
      archRatio: 0.5,
      heelRatio: 0.5,
      archHeight: fallbackArchHeight,
      archType: "Normal Arch",
      formatted: formatArchMeasurement(fallbackLength, fallbackWidth, fallbackArchHeight)
    };
  }
}

/**
 * Validate arch height measurements for quality assurance
 */
export function validateArchMeasurements(measurement: FootMeasurementWithArch): boolean {
  try {
    // Check if measurements are within realistic ranges
    const isLengthValid = measurement.length >= 20 && measurement.length <= 35;
    const isWidthValid = measurement.width >= 7 && measurement.width <= 15;
    const isArchHeightValid = measurement.archHeight >= 1.5 && measurement.archHeight <= 4.0;
    const isArchRatioValid = measurement.archRatio >= 0.25 && measurement.archRatio <= 0.75;
    
    // Check if arch height is reasonable percentage of foot length
    const archPercentage = measurement.archHeight / measurement.length;
    const isPercentageValid = archPercentage >= 0.05 && archPercentage <= 0.15;
    
    const isValid = isLengthValid && isWidthValid && isArchHeightValid && 
                   isArchRatioValid && isPercentageValid;
    
    if (!isValid) {
      log.warn('Arch measurement validation failed', 'ArchHeightCalculation', {
        measurement,
        validation: {
          isLengthValid,
          isWidthValid,
          isArchHeightValid,
          isArchRatioValid,
          isPercentageValid
        }
      });
    }
    
    return isValid;
    
  } catch (error) {
    log.error('Error validating arch measurements', 'ArchHeightCalculation', error);
    return false;
  }
}

// =============================================================================
// COMPETITIVE ADVANTAGE UTILITIES
// =============================================================================

/**
 * Get competitive advantage messaging for marketing/UI
 */
export function getCompetitiveAdvantageInfo() {
  return {
    uniqueFeature: "Only app measuring actual arch height in centimeters",
    displayFormat: "Length x Width x Arch Height",
    scientificBasis: "Based on anthropometric research (6-12% of foot length)",
    marketDifferentiation: "Professional measurement format",
    academicCredibility: "Real CNN training with arch height focus"
  };
}

/**
 * Export all functions for easy import
 */
export default {
  calculateArchHeight,
  classifyArchType,
  formatArchMeasurement,
  analyzeArchHeight,
  processCNNOutputWithArchHeight,
  validateArchMeasurements,
  getCompetitiveAdvantageInfo
};
