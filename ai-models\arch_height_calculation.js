
// 🏗️ FootFit Arch Height Calculation - Competitive Advantage
// Convert CNN output to Length x Width x Arch Height format

function calculateArchHeight(footLength, archRatio) {
    // Anthropometric conversion: arch_ratio (0.25-0.75) → 6-12% of foot length
    const archPercentage = 0.06 + (archRatio - 0.25) * (0.12 - 0.06) / 0.5;
    let archHeight = footLength * archPercentage;
    
    // Ensure realistic bounds (1.5-4.0cm)
    archHeight = Math.max(1.5, Math.min(4.0, archHeight));
    
    return archHeight;
}

function classifyArchType(archHeight, footLength) {
    const archPercentage = archHeight / footLength;
    
    if (archPercentage < 0.08) return "Low Arch (Flat Feet)";
    if (archPercentage > 0.10) return "High Arch";
    return "Normal Arch";
}

function formatArchMeasurement(length, width, archHeight) {
    return `${length.toFixed(1)} x ${width.toFixed(1)} x ${archHeight.toFixed(1)}cm`;
}

// 🚀 FootFit Integration Example:
async function processFootMeasurement(imageData) {
    // CNN prediction: [length, width, arch_ratio, heel_ratio]
    const predictions = await model.predict(imageData).data();
    const [length, width, archRatio, heelRatio] = predictions;
    
    // Convert to competitive advantage format
    const archHeight = calculateArchHeight(length, archRatio);
    const archType = classifyArchType(archHeight, length);
    const formatted = formatArchMeasurement(length, width, archHeight);
    
    return {
        measurements: { length, width, archHeight, heelRatio },
        archType,
        formatted,
        competitiveAdvantage: "Only app measuring actual arch height in cm"
    };
}
        